using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Extensions
{
    /// <summary>
    /// طرق تمديد لـ HttpClient لتسهيل العمليات الشائعة
    /// </summary>
    public static class HttpClientExtensions
    {
        /// <summary>
        /// إضافة توكن المصادقة JWT
        /// </summary>
        /// <param name="client">عميل HTTP</param>
        /// <param name="token">التوكن</param>
        public static void SetBearerToken(this HttpClient client, string token)
        {
            if (!string.IsNullOrEmpty(token))
            {
                client.DefaultRequestHeaders.Authorization = 
                    new AuthenticationHeaderValue("Bearer", token);
            }
        }

        /// <summary>
        /// إزالة توكن المصادقة
        /// </summary>
        /// <param name="client">عميل HTTP</param>
        public static void RemoveBearerToken(this HttpClient client)
        {
            client.DefaultRequestHeaders.Authorization = null;
        }

        /// <summary>
        /// إضافة رأس مخصص
        /// </summary>
        /// <param name="client">عميل HTTP</param>
        /// <param name="name">اسم الرأس</param>
        /// <param name="value">قيمة الرأس</param>
        public static void AddCustomHeader(this HttpClient client, string name, string value)
        {
            if (client.DefaultRequestHeaders.Contains(name))
            {
                client.DefaultRequestHeaders.Remove(name);
            }
            client.DefaultRequestHeaders.Add(name, value);
        }

        /// <summary>
        /// إرسال طلب POST مع JSON وإرجاع استجابة مخصصة
        /// </summary>
        /// <typeparam name="T">نوع البيانات المرسلة</typeparam>
        /// <typeparam name="TResult">نوع البيانات المستلمة</typeparam>
        /// <param name="client">عميل HTTP</param>
        /// <param name="requestUri">رابط الطلب</param>
        /// <param name="value">البيانات المرسلة</param>
        /// <param name="options">خيارات JSON</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>الاستجابة المحللة</returns>
        public static async Task<TResult?> PostAsJsonAsync<T, TResult>(
            this HttpClient client,
            string requestUri,
            T value,
            JsonSerializerOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var response = await client.PostAsJsonAsync(requestUri, value, options, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            return JsonSerializer.Deserialize<TResult>(content, options);
        }

        /// <summary>
        /// إرسال طلب PUT مع JSON وإرجاع استجابة مخصصة
        /// </summary>
        /// <typeparam name="T">نوع البيانات المرسلة</typeparam>
        /// <typeparam name="TResult">نوع البيانات المستلمة</typeparam>
        /// <param name="client">عميل HTTP</param>
        /// <param name="requestUri">رابط الطلب</param>
        /// <param name="value">البيانات المرسلة</param>
        /// <param name="options">خيارات JSON</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>الاستجابة المحللة</returns>
        public static async Task<TResult?> PutAsJsonAsync<T, TResult>(
            this HttpClient client,
            string requestUri,
            T value,
            JsonSerializerOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var response = await client.PutAsJsonAsync(requestUri, value, options, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            return JsonSerializer.Deserialize<TResult>(content, options);
        }

        /// <summary>
        /// إرسال طلب GET وإرجاع استجابة مخصصة
        /// </summary>
        /// <typeparam name="TResult">نوع البيانات المستلمة</typeparam>
        /// <param name="client">عميل HTTP</param>
        /// <param name="requestUri">رابط الطلب</param>
        /// <param name="options">خيارات JSON</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>الاستجابة المحللة</returns>
        public static async Task<TResult?> GetFromJsonAsync<TResult>(
            this HttpClient client,
            string requestUri,
            JsonSerializerOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var response = await client.GetAsync(requestUri, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            return JsonSerializer.Deserialize<TResult>(content, options);
        }

        /// <summary>
        /// إرسال طلب مع إعادة المحاولة
        /// </summary>
        /// <param name="client">عميل HTTP</param>
        /// <param name="request">الطلب</param>
        /// <param name="maxRetries">عدد المحاولات الأقصى</param>
        /// <param name="delay">التأخير بين المحاولات</param>
        /// <param name="cancellationToken">رمز الإلغاء</param>
        /// <returns>الاستجابة</returns>
        public static async Task<HttpResponseMessage> SendWithRetryAsync(
            this HttpClient client,
            HttpRequestMessage request,
            int maxRetries = 3,
            TimeSpan? delay = null,
            CancellationToken cancellationToken = default)
        {
            delay ??= TimeSpan.FromSeconds(1);
            
            for (int i = 0; i <= maxRetries; i++)
            {
                try
                {
                    // نسخ الطلب للمحاولات المتعددة
                    var clonedRequest = await CloneRequestAsync(request);
                    var response = await client.SendAsync(clonedRequest, cancellationToken);
                    
                    if (response.IsSuccessStatusCode || i == maxRetries)
                    {
                        return response;
                    }
                    
                    response.Dispose();
                }
                catch (Exception) when (i < maxRetries)
                {
                    // تجاهل الاستثناء إذا لم نصل للمحاولة الأخيرة
                }
                
                if (i < maxRetries)
                {
                    await Task.Delay(delay.Value, cancellationToken);
                }
            }
            
            // هذا لن يحدث أبداً، لكن المترجم يتطلبه
            throw new InvalidOperationException("فشل في إرسال الطلب بعد جميع المحاولات");
        }

        /// <summary>
        /// نسخ طلب HTTP
        /// </summary>
        /// <param name="original">الطلب الأصلي</param>
        /// <returns>نسخة من الطلب</returns>
        private static async Task<HttpRequestMessage> CloneRequestAsync(HttpRequestMessage original)
        {
            var clone = new HttpRequestMessage(original.Method, original.RequestUri);
            
            // نسخ الرؤوس
            foreach (var header in original.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
            
            // نسخ المحتوى إذا وجد
            if (original.Content != null)
            {
                var content = await original.Content.ReadAsByteArrayAsync();
                clone.Content = new ByteArrayContent(content);
                
                // نسخ رؤوس المحتوى
                foreach (var header in original.Content.Headers)
                {
                    clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }
            
            return clone;
        }

        /// <summary>
        /// بناء URL مع معاملات الاستعلام
        /// </summary>
        /// <param name="baseUrl">الرابط الأساسي</param>
        /// <param name="parameters">معاملات الاستعلام</param>
        /// <returns>الرابط الكامل</returns>
        public static string BuildUrlWithQuery(string baseUrl, Dictionary<string, object>? parameters = null)
        {
            if (parameters == null || !parameters.Any())
                return baseUrl;

            var queryString = string.Join("&", 
                parameters.Where(p => p.Value != null)
                         .Select(p => $"{Uri.EscapeDataString(p.Key)}={Uri.EscapeDataString(p.Value.ToString() ?? string.Empty)}"));

            var separator = baseUrl.Contains('?') ? "&" : "?";
            return $"{baseUrl}{separator}{queryString}";
        }
    }
}
