using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Stores
{
    public partial class ListStoreSettings
    {
        [Inject] IGRepository<ShopSettingsDTO> _ShopSettings { get; set; }
        [Inject] IDialogService DialogService { get; set; }


        private IEnumerable<ShopSettingsDTO> shopSettingsList = new List<ShopSettingsDTO>();
        private string searchString = "";
        private bool isLoading = false;
        private string NameStoreForDelete = "";
        private MudMessageBox mbox;

        protected override async Task OnInitializedAsync()
        {
            await LoadingData();
        }

        private async Task LoadingData()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                var res = await _ShopSettings.GetAll("ShopSettings/getAllShopSettings");
                if (res.response == null)
                {
                    shopSettingsList = res.list ?? new List<ShopSettingsDTO>();
                }
                else
                {
                    _snackbar.Add("خطأ في تحميل البيانات", Severity.Error);
                    shopSettingsList = new List<ShopSettingsDTO>();
                }
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في تحميل البيانات: {ex.Message}", Severity.Error);
                shopSettingsList = new List<ShopSettingsDTO>();
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private IEnumerable<ShopSettingsDTO> FilteredStores
        {
            get
            {
                if (string.IsNullOrWhiteSpace(searchString))
                    return shopSettingsList;

                return shopSettingsList.Where(store =>
                    (store.StoreName?.Contains(searchString, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (store.CompanyName?.Contains(searchString, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (store.CompanyPhone?.Contains(searchString, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (store.StoreAddress?.Contains(searchString, StringComparison.OrdinalIgnoreCase) ?? false)
                );
            }
        }

        private async Task SearchStores()
        {
            // البحث يتم تلقائياً من خلال FilteredStores
            StateHasChanged();
        }

        private async Task Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertStoreSettings>();
            parameters.Add(x => x.id, id);
            
            var options = new DialogOptions() 
            { 
                CloseButton = false, 
                MaxWidth = MaxWidth.Large, 
                FullWidth = true,
                CloseOnEscapeKey = true
            };
            
            var result = await DialogService.Show<UpsertStoreSettings>(
                id != Guid.Empty ? "تعديل إعدادات المتجر" : "إضافة إعدادات متجر جديد", 
                parameters, 
                options).Result;
                
            if ((bool?)result.Data == true) 
            {
                await LoadingData();
            }
        }

        private async Task Delete(ShopSettingsDTO obj)
        {
            NameStoreForDelete = obj.StoreName ?? "غير محدد";
            bool? result = await mbox.ShowAsync();

            if (result == true)
            {
                try
                {
                    var response = await _ShopSettings.Delete("ShopSettings/deleteShopSettings", obj.Id);
                    if (response.State)
                    {
                        _snackbar.Add(response.Message, Severity.Success);
                        await LoadingData();
                    }
                    else
                    {
                        _snackbar.Add(response.Message, Severity.Error);
                    }
                }
                catch (Exception ex)
                {
                    _snackbar.Add($"خطأ في حذف البيانات: {ex.Message}", Severity.Error);
                }
            }
        }
    }
}
