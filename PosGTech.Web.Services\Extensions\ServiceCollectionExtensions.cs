using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using PosGTech.Web.Services.Configuration;
using PosGTech.Web.Services.Implementations;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Headers;

namespace PosGTech.Web.Services.Extensions
{
    /// <summary>
    /// طرق تمديد لتسجيل خدمات API في Dependency Injection
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// تسجيل خدمات API مع التكوين الافتراضي
        /// </summary>
        /// <param name="services">مجموعة الخدمات</param>
        /// <param name="configuration">تكوين التطبيق</param>
        /// <returns>مجموعة الخدمات للتسلسل</returns>
        public static IServiceCollection AddApiServices(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // تسجيل خيارات التكوين
            services.Configure<ApiServiceOptions>(
                configuration.GetSection(ApiServiceOptions.SectionName));

            // تسجيل HttpClient مع التكوين
            services.AddHttpClient("ApiClient", (serviceProvider, client) =>
            {
                var options = serviceProvider.GetRequiredService<IOptions<ApiServiceOptions>>().Value;
                
                // تكوين الرابط الأساسي
                if (!string.IsNullOrEmpty(options.BaseUrl))
                {
                    client.BaseAddress = new Uri(options.BaseUrl);
                }

                // تكوين الرؤوس الافتراضية
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(
                    new MediaTypeWithQualityHeaderValue("application/json"));

                // إضافة الرؤوس المخصصة
                foreach (var header in options.DefaultHeaders)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }

                // تكوين مهلة الانتظار
                client.Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds);
            });

            // تسجيل المستودع العام
            services.AddScoped(typeof(IApiRepository<>), typeof(ApiRepository<>));

            return services;
        }

        /// <summary>
        /// تسجيل خدمات API مع تكوين مخصص
        /// </summary>
        /// <param name="services">مجموعة الخدمات</param>
        /// <param name="configureOptions">دالة تكوين الخيارات</param>
        /// <returns>مجموعة الخدمات للتسلسل</returns>
        public static IServiceCollection AddApiServices(
            this IServiceCollection services,
            Action<ApiServiceOptions> configureOptions)
        {
            // تسجيل خيارات التكوين
            services.Configure(configureOptions);

            // تسجيل HttpClient مع التكوين
            services.AddHttpClient("ApiClient", (serviceProvider, client) =>
            {
                var options = serviceProvider.GetRequiredService<IOptions<ApiServiceOptions>>().Value;
                
                // تكوين الرابط الأساسي
                if (!string.IsNullOrEmpty(options.BaseUrl))
                {
                    client.BaseAddress = new Uri(options.BaseUrl);
                }

                // تكوين الرؤوس الافتراضية
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(
                    new MediaTypeWithQualityHeaderValue("application/json"));

                // إضافة الرؤوس المخصصة
                foreach (var header in options.DefaultHeaders)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }

                // تكوين مهلة الانتظار
                client.Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds);
            });

            // تسجيل المستودع العام
            services.AddScoped(typeof(IApiRepository<>), typeof(ApiRepository<>));

            return services;
        }

        /// <summary>
        /// تسجيل خدمات API مع دعم المصادقة JWT
        /// </summary>
        /// <param name="services">مجموعة الخدمات</param>
        /// <param name="configuration">تكوين التطبيق</param>
        /// <param name="tokenProvider">مزود التوكن</param>
        /// <returns>مجموعة الخدمات للتسلسل</returns>
        public static IServiceCollection AddApiServicesWithAuth(
            this IServiceCollection services,
            IConfiguration configuration,
            Func<IServiceProvider, Task<string?>> tokenProvider)
        {
            // تسجيل خيارات التكوين
            services.Configure<ApiServiceOptions>(
                configuration.GetSection(ApiServiceOptions.SectionName));

            // تسجيل HttpClient مع دعم المصادقة
            services.AddHttpClient("ApiClient", async (serviceProvider, client) =>
            {
                var options = serviceProvider.GetRequiredService<IOptions<ApiServiceOptions>>().Value;
                
                // تكوين الرابط الأساسي
                if (!string.IsNullOrEmpty(options.BaseUrl))
                {
                    client.BaseAddress = new Uri(options.BaseUrl);
                }

                // تكوين الرؤوس الافتراضية
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(
                    new MediaTypeWithQualityHeaderValue("application/json"));

                // إضافة توكن المصادقة
                var token = await tokenProvider(serviceProvider);
                if (!string.IsNullOrEmpty(token))
                {
                    client.DefaultRequestHeaders.Authorization = 
                        new AuthenticationHeaderValue("Bearer", token);
                }

                // إضافة الرؤوس المخصصة
                foreach (var header in options.DefaultHeaders)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }

                // تكوين مهلة الانتظار
                client.Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds);
            });

            // تسجيل المستودع العام
            services.AddScoped(typeof(IApiRepository<>), typeof(ApiRepository<>));

            return services;
        }

        /// <summary>
        /// إضافة مستودع مخصص لنوع معين
        /// </summary>
        /// <typeparam name="TEntity">نوع الكائن</typeparam>
        /// <typeparam name="TRepository">نوع المستودع</typeparam>
        /// <param name="services">مجموعة الخدمات</param>
        /// <returns>مجموعة الخدمات للتسلسل</returns>
        public static IServiceCollection AddCustomApiRepository<TEntity, TRepository>(
            this IServiceCollection services)
            where TEntity : class
            where TRepository : class, IApiRepository<TEntity>
        {
            services.AddScoped<IApiRepository<TEntity>, TRepository>();
            return services;
        }

        /// <summary>
        /// تكوين خيارات JSON للتسلسل
        /// </summary>
        /// <param name="services">مجموعة الخدمات</param>
        /// <param name="configureJson">دالة تكوين JSON</param>
        /// <returns>مجموعة الخدمات للتسلسل</returns>
        public static IServiceCollection ConfigureApiJsonOptions(
            this IServiceCollection services,
            Action<System.Text.Json.JsonSerializerOptions> configureJson)
        {
            services.PostConfigure<ApiServiceOptions>(options =>
            {
                // يمكن إضافة تكوين JSON هنا إذا لزم الأمر
            });

            return services;
        }
    }
}
