using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using MudBlazor.Services;
using PosGTech.ModelsDTO.Items;
using PosGTech.Web.Services.Extensions;
using PosGTech.Web.Services.Specialized;

namespace PosGTech.Web.Services.Examples
{
    /// <summary>
    /// مثال على كيفية تكوين PosGTech.Web.Services في Program.cs
    /// </summary>
    public class ProgramExample
    {
        public static async Task Main(string[] args)
        {
            var builder = WebAssemblyHostBuilder.CreateDefault(args);
            builder.RootComponents.Add<App>("#app");
            builder.RootComponents.Add<HeadOutlet>("head::after");

            // ===== تكوين خدمات API =====

            // الطريقة الأولى: التكوين الأساسي من appsettings.json
            builder.Services.AddApiServices(builder.Configuration);

            // الطريقة الثانية: التكوين المخصص
            builder.Services.AddApiServices(options =>
            {
                options.BaseUrl = "https://localhost:7282/api/";
                options.TimeoutSeconds = 30;
                options.RetryCount = 3;
                options.EnableLogging = true;
                options.DefaultPageSize = 10;
                options.MaxPageSize = 100;
                options.DefaultHeaders.Add("Accept", "application/json");
                options.DefaultHeaders.Add("User-Agent", "PosGTech-WebClient/1.0");
            });

            // الطريقة الثالثة: التكوين مع دعم المصادقة JWT
            builder.Services.AddApiServicesWithAuth(
                builder.Configuration,
                async serviceProvider =>
                {
                    try
                    {
                        var localStorage = serviceProvider.GetRequiredService<ILocalStorageService>();
                        var token = await localStorage.GetItemAsStringAsync("authToken");
                        return token;
                    }
                    catch
                    {
                        return null;
                    }
                });

            // ===== تسجيل المستودعات المخصصة =====

            // تسجيل مستودع الأصناف المخصص
            builder.Services.AddCustomApiRepository<ItemDTO, ItemApiRepository>();

            // يمكن إضافة مستودعات مخصصة أخرى
            // builder.Services.AddCustomApiRepository<CategoryDTO, CategoryApiRepository>();
            // builder.Services.AddCustomApiRepository<ClientDTO, ClientApiRepository>();

            // ===== تكوين الخدمات الأخرى =====

            // خدمات MudBlazor
            builder.Services.AddMudServices();

            // خدمات التخزين المحلي
            builder.Services.AddBlazoredLocalStorage();

            // خدمات المصادقة
            builder.Services.AddAuthorizationCore();
            builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthenticationStateProvider>();

            // ===== تكوين HttpClient إضافي (اختياري) =====

            // يمكن إضافة HttpClient إضافي للخدمات الأخرى
            builder.Services.AddHttpClient("ReportsClient", client =>
            {
                client.BaseAddress = new Uri("https://localhost:7282/api/reports/");
                client.Timeout = TimeSpan.FromMinutes(5); // مهلة أطول للتقارير
            });

            // ===== تكوين خيارات JSON (اختياري) =====

            builder.Services.ConfigureApiJsonOptions(options =>
            {
                options.PropertyNameCaseInsensitive = true;
                options.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
                options.WriteIndented = false;
            });

            // ===== بناء وتشغيل التطبيق =====

            var app = builder.Build();

            // تهيئة إضافية إذا لزم الأمر
            await InitializeApplicationAsync(app);

            await app.RunAsync();
        }

        /// <summary>
        /// تهيئة إضافية للتطبيق
        /// </summary>
        private static async Task InitializeApplicationAsync(WebAssemblyHost app)
        {
            try
            {
                // يمكن إضافة تهيئة إضافية هنا
                // مثل التحقق من الاتصال بـ API أو تحميل البيانات الأساسية

                var itemRepository = app.Services.GetRequiredService<IItemApiRepository>();
                
                // اختبار الاتصال بـ API
                var testResponse = await itemRepository.CountAsync("Items/count");
                Console.WriteLine($"API Connection Test: Found {testResponse} items");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Initialization warning: {ex.Message}");
                // لا نوقف التطبيق في حالة فشل التهيئة
            }
        }
    }

    /// <summary>
    /// مزود حالة المصادقة المخصص
    /// </summary>
    public class CustomAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly ILocalStorageService _localStorage;

        public CustomAuthenticationStateProvider(ILocalStorageService localStorage)
        {
            _localStorage = localStorage;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                var token = await _localStorage.GetItemAsStringAsync("authToken");
                
                if (string.IsNullOrEmpty(token))
                {
                    return new AuthenticationState(new System.Security.Claims.ClaimsPrincipal());
                }

                // تحليل التوكن واستخراج Claims
                var claims = ParseTokenClaims(token);
                var identity = new System.Security.Claims.ClaimsIdentity(claims, "jwt");
                var user = new System.Security.Claims.ClaimsPrincipal(identity);

                return new AuthenticationState(user);
            }
            catch
            {
                return new AuthenticationState(new System.Security.Claims.ClaimsPrincipal());
            }
        }

        private List<System.Security.Claims.Claim> ParseTokenClaims(string token)
        {
            // تنفيذ تحليل JWT Token واستخراج Claims
            // هذا مثال مبسط - في التطبيق الحقيقي يجب استخدام مكتبة JWT
            
            var claims = new List<System.Security.Claims.Claim>();
            
            try
            {
                // تحليل التوكن هنا
                // var handler = new JwtSecurityTokenHandler();
                // var jsonToken = handler.ReadJwtToken(token);
                // claims.AddRange(jsonToken.Claims);
            }
            catch
            {
                // في حالة فشل التحليل، إرجاع قائمة فارغة
            }

            return claims;
        }

        public void NotifyAuthenticationStateChanged()
        {
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }
    }

    /// <summary>
    /// مثال على تكوين متقدم للخدمات
    /// </summary>
    public static class AdvancedServiceConfiguration
    {
        /// <summary>
        /// تكوين خدمات API مع إعدادات متقدمة
        /// </summary>
        public static IServiceCollection AddAdvancedApiServices(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // تكوين أساسي
            services.AddApiServices(configuration);

            // إضافة معالج مخصص للأخطاء
            services.AddScoped<IApiErrorHandler, CustomApiErrorHandler>();

            // إضافة خدمة التخزين المؤقت
            services.AddScoped<IApiCacheService, ApiCacheService>();

            // إضافة خدمة المراقبة والتسجيل
            services.AddScoped<IApiMonitoringService, ApiMonitoringService>();

            return services;
        }
    }

    // واجهات وفئات مساعدة للمثال
    public interface IApiErrorHandler
    {
        Task HandleErrorAsync(Exception exception, string endpoint);
    }

    public class CustomApiErrorHandler : IApiErrorHandler
    {
        public async Task HandleErrorAsync(Exception exception, string endpoint)
        {
            // تنفيذ معالجة الأخطاء المخصصة
            Console.WriteLine($"API Error at {endpoint}: {exception.Message}");
            await Task.CompletedTask;
        }
    }

    public interface IApiCacheService
    {
        Task<T?> GetAsync<T>(string key);
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null);
    }

    public class ApiCacheService : IApiCacheService
    {
        public async Task<T?> GetAsync<T>(string key)
        {
            // تنفيذ التخزين المؤقت
            await Task.CompletedTask;
            return default(T);
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            // تنفيذ التخزين المؤقت
            await Task.CompletedTask;
        }
    }

    public interface IApiMonitoringService
    {
        Task LogRequestAsync(string endpoint, TimeSpan duration, bool success);
    }

    public class ApiMonitoringService : IApiMonitoringService
    {
        public async Task LogRequestAsync(string endpoint, TimeSpan duration, bool success)
        {
            // تنفيذ المراقبة والتسجيل
            Console.WriteLine($"API Request: {endpoint} - Duration: {duration.TotalMilliseconds}ms - Success: {success}");
            await Task.CompletedTask;
        }
    }
}
