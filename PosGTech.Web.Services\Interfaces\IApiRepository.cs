using PosGTech.ModelsDTO;
using System.Linq.Expressions;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// واجهة عامة للتعامل مع API endpoints
    /// تحاكي نفس مفهوم IRepository لكن للاتصال بـ API
    /// </summary>
    /// <typeparam name="T">نوع الكائن DTO</typeparam>
    public interface IApiRepository<T> where T : class
    {
        #region Basic CRUD Operations

        /// <summary>
        /// جلب جميع العناصر من API
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="includeProperties">الخصائص المراد تضمينها</param>
        /// <returns>قائمة بالعناصر أو رسالة خطأ</returns>
        Task<ApiResponse<IEnumerable<T>>> GetAllAsync(string endpoint, string? includeProperties = null);

        /// <summary>
        /// جلب عنصر واحد بالمعرف
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="id">معرف العنصر</param>
        /// <returns>العنصر المطلوب أو رسالة خطأ</returns>
        Task<ApiResponse<T>> GetByIdAsync(string endpoint, Guid id);

        /// <summary>
        /// إنشاء عنصر جديد
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="entity">العنصر المراد إنشاؤه</param>
        /// <returns>نتيجة العملية</returns>
        Task<ResponseVM> CreateAsync(string endpoint, T entity);

        /// <summary>
        /// تحديث عنصر موجود
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="id">معرف العنصر</param>
        /// <param name="entity">البيانات المحدثة</param>
        /// <returns>نتيجة العملية</returns>
        Task<ResponseVM> UpdateAsync(string endpoint, Guid id, T entity);

        /// <summary>
        /// حذف عنصر
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="id">معرف العنصر</param>
        /// <returns>نتيجة العملية</returns>
        Task<ResponseVM> DeleteAsync(string endpoint, Guid id);

        #endregion

        #region Advanced Operations

        /// <summary>
        /// جلب العناصر مع التصفية والترتيب
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="filter">معايير التصفية</param>
        /// <param name="orderBy">معايير الترتيب</param>
        /// <param name="includeProperties">الخصائص المراد تضمينها</param>
        /// <returns>قائمة مفلترة ومرتبة</returns>
        Task<ApiResponse<IEnumerable<T>>> GetFilteredAsync(
            string endpoint, 
            Dictionary<string, object>? filter = null,
            string? orderBy = null,
            string? includeProperties = null);

        /// <summary>
        /// جلب العناصر مع الترقيم
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="filter">معايير التصفية</param>
        /// <param name="orderBy">معايير الترتيب</param>
        /// <returns>صفحة من العناصر</returns>
        Task<ApiResponse<PagedResult<T>>> GetPagedAsync(
            string endpoint,
            int pageNumber = 1,
            int pageSize = 10,
            Dictionary<string, object>? filter = null,
            string? orderBy = null);

        /// <summary>
        /// تنفيذ استعلام مخصص
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="parameters">معاملات الاستعلام</param>
        /// <returns>نتيجة الاستعلام</returns>
        Task<ApiResponse<TResult>> ExecuteCustomQueryAsync<TResult>(
            string endpoint, 
            Dictionary<string, object>? parameters = null) where TResult : class;

        #endregion

        #region Utility Methods

        /// <summary>
        /// التحقق من وجود عنصر
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="id">معرف العنصر</param>
        /// <returns>true إذا كان العنصر موجود</returns>
        Task<bool> ExistsAsync(string endpoint, Guid id);

        /// <summary>
        /// عد العناصر
        /// </summary>
        /// <param name="endpoint">نقطة النهاية في API</param>
        /// <param name="filter">معايير التصفية</param>
        /// <returns>عدد العناصر</returns>
        Task<int> CountAsync(string endpoint, Dictionary<string, object>? filter = null);

        #endregion
    }

    /// <summary>
    /// استجابة API مع البيانات
    /// </summary>
    /// <typeparam name="T">نوع البيانات</typeparam>
    public class ApiResponse<T>
    {
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string? ErrorMessage { get; set; }
        public int StatusCode { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// نتيجة مرقمة للعناصر
    /// </summary>
    /// <typeparam name="T">نوع العناصر</typeparam>
    public class PagedResult<T>
    {
        public IEnumerable<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
