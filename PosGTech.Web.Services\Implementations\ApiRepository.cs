using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PosGTech.ModelsDTO;
using PosGTech.Web.Services.Configuration;
using PosGTech.Web.Services.Interfaces;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ عام للتعامل مع API endpoints
    /// </summary>
    /// <typeparam name="T">نوع الكائن DTO</typeparam>
    public class ApiRepository<T> : IApiRepository<T> where T : class
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ApiRepository<T>> _logger;
        private readonly ApiServiceOptions _options;
        private readonly JsonSerializerOptions _jsonOptions;

        public ApiRepository(
            HttpClient httpClient,
            ILogger<ApiRepository<T>> logger,
            IOptions<ApiServiceOptions> options)
        {
            _httpClient = httpClient;
            _logger = logger;
            _options = options.Value;

            // تكوين خيارات JSON
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            // تكوين HttpClient
            ConfigureHttpClient();
        }

        #region Basic CRUD Operations

        public async Task<ApiResponse<IEnumerable<T>>> GetAllAsync(string endpoint, string? includeProperties = null)
        {
            try
            {
                var url = BuildUrl(endpoint, new Dictionary<string, object>
                {
                    { "includeProperties", includeProperties ?? string.Empty }
                });

                _logger.LogInformation("Getting all items from {Endpoint}", endpoint);

                var response = await _httpClient.GetAsync(url);
                return await ProcessResponse<IEnumerable<T>>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all items from {Endpoint}", endpoint);
                return CreateErrorResponse<IEnumerable<T>>(ex.Message);
            }
        }

        public async Task<ApiResponse<T>> GetByIdAsync(string endpoint, Guid id)
        {
            try
            {
                var url = $"{endpoint}/{id}";
                _logger.LogInformation("Getting item {Id} from {Endpoint}", id, endpoint);

                var response = await _httpClient.GetAsync(url);
                return await ProcessResponse<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item {Id} from {Endpoint}", id, endpoint);
                return CreateErrorResponse<T>(ex.Message);
            }
        }

        public async Task<ResponseVM> CreateAsync(string endpoint, T entity)
        {
            try
            {
                _logger.LogInformation("Creating new item at {Endpoint}", endpoint);

                var response = await _httpClient.PostAsJsonAsync(endpoint, entity, _jsonOptions);
                return await ProcessResponseVM(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating item at {Endpoint}", endpoint);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        public async Task<ResponseVM> UpdateAsync(string endpoint, Guid id, T entity)
        {
            try
            {
                var url = $"{endpoint}/{id}";
                _logger.LogInformation("Updating item {Id} at {Endpoint}", id, endpoint);

                var response = await _httpClient.PutAsJsonAsync(url, entity, _jsonOptions);
                return await ProcessResponseVM(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item {Id} at {Endpoint}", id, endpoint);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        public async Task<ResponseVM> DeleteAsync(string endpoint, Guid id)
        {
            try
            {
                var url = $"{endpoint}/{id}";
                _logger.LogInformation("Deleting item {Id} from {Endpoint}", id, endpoint);

                var response = await _httpClient.DeleteAsync(url);
                return await ProcessResponseVM(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item {Id} from {Endpoint}", id, endpoint);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        #endregion

        #region Advanced Operations

        public async Task<ApiResponse<IEnumerable<T>>> GetFilteredAsync(
            string endpoint,
            Dictionary<string, object>? filter = null,
            string? orderBy = null,
            string? includeProperties = null)
        {
            try
            {
                var parameters = new Dictionary<string, object>();
                
                if (filter != null)
                    foreach (var kvp in filter)
                        parameters[kvp.Key] = kvp.Value;

                if (!string.IsNullOrEmpty(orderBy))
                    parameters["orderBy"] = orderBy;

                if (!string.IsNullOrEmpty(includeProperties))
                    parameters["includeProperties"] = includeProperties;

                var url = BuildUrl(endpoint, parameters);
                _logger.LogInformation("Getting filtered items from {Endpoint}", endpoint);

                var response = await _httpClient.GetAsync(url);
                return await ProcessResponse<IEnumerable<T>>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filtered items from {Endpoint}", endpoint);
                return CreateErrorResponse<IEnumerable<T>>(ex.Message);
            }
        }

        public async Task<ApiResponse<PagedResult<T>>> GetPagedAsync(
            string endpoint,
            int pageNumber = 1,
            int pageSize = 10,
            Dictionary<string, object>? filter = null,
            string? orderBy = null)
        {
            try
            {
                // التحقق من حدود الترقيم
                pageSize = Math.Min(pageSize, _options.MaxPageSize);
                pageNumber = Math.Max(pageNumber, 1);

                var parameters = new Dictionary<string, object>
                {
                    { "pageNumber", pageNumber },
                    { "pageSize", pageSize }
                };

                if (filter != null)
                    foreach (var kvp in filter)
                        parameters[kvp.Key] = kvp.Value;

                if (!string.IsNullOrEmpty(orderBy))
                    parameters["orderBy"] = orderBy;

                var url = BuildUrl(endpoint, parameters);
                _logger.LogInformation("Getting paged items from {Endpoint} (Page: {PageNumber}, Size: {PageSize})", 
                    endpoint, pageNumber, pageSize);

                var response = await _httpClient.GetAsync(url);
                return await ProcessResponse<PagedResult<T>>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paged items from {Endpoint}", endpoint);
                return CreateErrorResponse<PagedResult<T>>(ex.Message);
            }
        }

        public async Task<ApiResponse<TResult>> ExecuteCustomQueryAsync<TResult>(
            string endpoint,
            Dictionary<string, object>? parameters = null) where TResult : class
        {
            try
            {
                var url = BuildUrl(endpoint, parameters);
                _logger.LogInformation("Executing custom query at {Endpoint}", endpoint);

                var response = await _httpClient.GetAsync(url);
                return await ProcessResponse<TResult>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing custom query at {Endpoint}", endpoint);
                return CreateErrorResponse<TResult>(ex.Message);
            }
        }

        #endregion

        #region Utility Methods

        public async Task<bool> ExistsAsync(string endpoint, Guid id)
        {
            try
            {
                var response = await GetByIdAsync(endpoint, id);
                return response.IsSuccess && response.Data != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<int> CountAsync(string endpoint, Dictionary<string, object>? filter = null)
        {
            try
            {
                var countEndpoint = $"{endpoint}/count";
                var url = BuildUrl(countEndpoint, filter);
                
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var countResult = await response.Content.ReadFromJsonAsync<int>(_jsonOptions);
                    return countResult;
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error counting items at {Endpoint}", endpoint);
                return 0;
            }
        }

        #endregion

        #region Private Helper Methods

        private void ConfigureHttpClient()
        {
            // تكوين الرؤوس الافتراضية
            foreach (var header in _options.DefaultHeaders)
            {
                _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }

            // تكوين مهلة الانتظار
            _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);
        }

        private string BuildUrl(string endpoint, Dictionary<string, object>? parameters = null)
        {
            if (parameters == null || !parameters.Any())
                return endpoint;

            var queryString = string.Join("&", 
                parameters.Where(p => p.Value != null)
                         .Select(p => $"{Uri.EscapeDataString(p.Key)}={Uri.EscapeDataString(p.Value.ToString() ?? string.Empty)}"));

            return $"{endpoint}?{queryString}";
        }

        private async Task<ApiResponse<TData>> ProcessResponse<TData>(HttpResponseMessage response)
        {
            var apiResponse = new ApiResponse<TData>
            {
                StatusCode = (int)response.StatusCode
            };

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var content = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrEmpty(content))
                    {
                        apiResponse.Data = JsonSerializer.Deserialize<TData>(content, _jsonOptions);
                        apiResponse.IsSuccess = true;
                    }
                    else
                    {
                        apiResponse.IsSuccess = true;
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Error deserializing response");
                    apiResponse.ErrorMessage = "خطأ في تحليل البيانات المستلمة";
                    apiResponse.IsSuccess = false;
                }
            }
            else
            {
                apiResponse.ErrorMessage = await GetErrorMessage(response);
                apiResponse.IsSuccess = false;
            }

            return apiResponse;
        }

        private async Task<ResponseVM> ProcessResponseVM(HttpResponseMessage response)
        {
            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var content = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrEmpty(content))
                    {
                        var result = JsonSerializer.Deserialize<ResponseVM>(content, _jsonOptions);
                        return result ?? new ResponseVM { State = true, Message = "تمت العملية بنجاح" };
                    }
                    return new ResponseVM { State = true, Message = "تمت العملية بنجاح" };
                }
                catch (JsonException)
                {
                    return new ResponseVM { State = true, Message = "تمت العملية بنجاح" };
                }
            }
            else
            {
                var errorMessage = await GetErrorMessage(response);
                return new ResponseVM { State = false, Message = errorMessage };
            }
        }

        private async Task<string> GetErrorMessage(HttpResponseMessage response)
        {
            try
            {
                var content = await response.Content.ReadAsStringAsync();
                
                // محاولة تحليل رسالة الخطأ من ResponseVM
                if (!string.IsNullOrEmpty(content))
                {
                    try
                    {
                        var errorResponse = JsonSerializer.Deserialize<ResponseVM>(content, _jsonOptions);
                        if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.Message))
                            return errorResponse.Message;
                    }
                    catch
                    {
                        // إذا فشل التحليل، استخدم المحتوى كما هو
                        return content;
                    }
                }

                // رسائل خطأ افتراضية حسب رمز الحالة
                return response.StatusCode switch
                {
                    HttpStatusCode.NotFound => "العنصر المطلوب غير موجود",
                    HttpStatusCode.Unauthorized => "غير مصرح لك بالوصول",
                    HttpStatusCode.Forbidden => "ليس لديك صلاحية لتنفيذ هذه العملية",
                    HttpStatusCode.BadRequest => "طلب غير صحيح",
                    HttpStatusCode.InternalServerError => "خطأ في الخادم",
                    _ => $"خطأ في الاتصال: {response.StatusCode}"
                };
            }
            catch
            {
                return $"خطأ في الاتصال: {response.StatusCode}";
            }
        }

        private static ApiResponse<TData> CreateErrorResponse<TData>(string errorMessage)
        {
            return new ApiResponse<TData>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                StatusCode = 0
            };
        }

        #endregion
    }
}
